use crate::types::SubscriptionType;
use crate::websocket::subscription::{ClientId, SubscriptionManager};
use crate::{BacktestError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::mpsc;
use tokio_tungstenite::tungstenite::Message;
use tracing::{debug, error, info, warn};

/// Binance风格的订阅消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceSubscribeMessage {
    pub method: String,
    pub params: Vec<String>,
    pub id: Option<u64>,
}

/// Binance风格的取消订阅消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceUnsubscribeMessage {
    pub method: String,
    pub params: Vec<String>,
    pub id: Option<u64>,
}

/// Binance风格的响应消息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BinanceResponse {
    pub result: Option<serde_json::Value>,
    pub id: Option<u64>,
    pub error: Option<BinanceError>,
}

/// Binance错误信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceError {
    pub code: i32,
    pub msg: String,
}

/// Book Ticker数据格式（Binance兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceBookTicker {
    #[serde(rename = "e")]
    pub event_type: String,
    #[serde(rename = "E")]
    pub event_time: u64,
    #[serde(rename = "T")]
    pub transaction_time: u64,
    #[serde(rename = "s")]
    pub symbol: String,
    #[serde(rename = "u")]
    pub update_id: u64,
    #[serde(rename = "b")]
    pub best_bid_price: String,
    #[serde(rename = "B")]
    pub best_bid_qty: String,
    #[serde(rename = "a")]
    pub best_ask_price: String,
    #[serde(rename = "A")]
    pub best_ask_qty: String,
}

/// Depth Update数据格式（Binance兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceDepthUpdate {
    #[serde(rename = "e")]
    pub event_type: String,
    #[serde(rename = "E")]
    pub event_time: u64,
    #[serde(rename = "T")]
    pub transaction_time: u64,
    #[serde(rename = "s")]
    pub symbol: String,
    #[serde(rename = "U")]
    pub first_update_id: u64,
    #[serde(rename = "u")]
    pub final_update_id: u64,
    #[serde(rename = "pu")]
    pub prev_final_update_id: u64,
    #[serde(rename = "b")]
    pub bids: Vec<[String; 2]>,
    #[serde(rename = "a")]
    pub asks: Vec<[String; 2]>,
}

/// Aggregate Trade数据格式（Binance兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinanceAggTrade {
    #[serde(rename = "e")]
    pub event_type: String,
    #[serde(rename = "E")]
    pub event_time: u64,
    #[serde(rename = "s")]
    pub symbol: String,
    #[serde(rename = "a")]
    pub agg_trade_id: u64,
    #[serde(rename = "p")]
    pub price: String,
    #[serde(rename = "q")]
    pub quantity: String,
    #[serde(rename = "f")]
    pub first_trade_id: u64,
    #[serde(rename = "l")]
    pub last_trade_id: u64,
    #[serde(rename = "T")]
    pub trade_time: u64,
    #[serde(rename = "m")]
    pub is_buyer_maker: bool,
}

/// 组合流包装格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamWrapper {
    pub stream: String,
    pub data: serde_json::Value,
}

/// WebSocket消息类型（保持向后兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WebSocketMessage {
    /// 订阅请求
    Subscribe { subscription: SubscriptionType },
    /// 取消订阅请求
    Unsubscribe { subscription: SubscriptionType },
    /// 心跳
    Ping,
    /// 心跳响应
    Pong,
    /// 错误消息
    Error { message: String },
    /// 成功响应
    Success { message: String },
    /// 数据推送
    Data {
        subscription: SubscriptionType,
        data: serde_json::Value,
    },
}

/// WebSocket处理器
pub struct WebSocketHandler {
    client_id: ClientId,
    subscription_manager: Arc<SubscriptionManager>,
    message_tx: mpsc::Sender<String>,
    /// 当前订阅的流（用于Binance风格的订阅）
    subscribed_streams: HashMap<String, SubscriptionType>,
}

impl WebSocketHandler {
    /// 创建新的WebSocket处理器
    pub fn new(
        client_id: ClientId,
        subscription_manager: Arc<SubscriptionManager>,
        message_tx: mpsc::Sender<String>,
    ) -> Self {
        Self {
            client_id,
            subscription_manager,
            message_tx,
            subscribed_streams: HashMap::new(),
        }
    }

    /// 处理WebSocket消息
    pub async fn handle_message(&mut self, message: Message) -> Result<()> {
        match message {
            Message::Text(text) => {
                self.handle_text_message(text).await?;
            }
            Message::Binary(_) => {
                warn!(
                    "Received binary message from client {}, ignoring",
                    self.client_id
                );
            }
            Message::Ping(payload) => {
                debug!("Received ping from client {}", self.client_id);
                self.send_pong(payload).await?;
            }
            Message::Pong(_) => {
                debug!("Received pong from client {}", self.client_id);
            }
            Message::Close(_) => {
                info!("Client {} requested close", self.client_id);
                self.handle_disconnect().await;
            }
            Message::Frame(_) => {
                // 原始帧，通常不需要处理
            }
        }

        Ok(())
    }

    /// 处理文本消息
    async fn handle_text_message(&mut self, text: String) -> Result<()> {
        debug!(
            "Received text message from client {}: {}",
            self.client_id, text
        );

        // 首先尝试解析为Binance风格的订阅消息
        if let Ok(subscribe_msg) = serde_json::from_str::<BinanceSubscribeMessage>(&text) {
            if subscribe_msg.method == "SUBSCRIBE" {
                return self.handle_binance_subscribe(subscribe_msg).await;
            }
        }

        // 尝试解析为Binance风格的取消订阅消息
        if let Ok(unsubscribe_msg) = serde_json::from_str::<BinanceUnsubscribeMessage>(&text) {
            if unsubscribe_msg.method == "UNSUBSCRIBE" {
                return self.handle_binance_unsubscribe(unsubscribe_msg).await;
            }
        }

        // 回退到原有的消息格式（保持向后兼容）
        if let Ok(ws_message) = serde_json::from_str::<WebSocketMessage>(&text) {
            match ws_message {
                WebSocketMessage::Subscribe { subscription } => {
                    self.handle_subscribe(subscription).await?;
                }
                WebSocketMessage::Unsubscribe { subscription } => {
                    self.handle_unsubscribe(subscription).await?;
                }
                WebSocketMessage::Ping => {
                    self.send_pong(Vec::new()).await?;
                }
                _ => {
                    warn!("Unexpected message type from client {}", self.client_id);
                    self.send_error("Unexpected message type".to_string())
                        .await?;
                }
            }
            return Ok(());
        }

        warn!(
            "Unknown message format from client {}: {}",
            self.client_id, text
        );
        self.send_binance_error("Unknown message format", None)
            .await?;
        Ok(())
    }

    /// 处理订阅请求
    async fn handle_subscribe(&self, subscription_type: SubscriptionType) -> Result<()> {
        let success = self
            .subscription_manager
            .subscribe(&self.client_id, subscription_type.clone());

        if success {
            let response = WebSocketMessage::Success {
                message: format!("Subscribed to {:?}", subscription_type),
            };
            self.send_message(response).await?;
            info!(
                "Client {} subscribed to {:?}",
                self.client_id, subscription_type
            );
        } else {
            let response = WebSocketMessage::Error {
                message: format!("Failed to subscribe to {:?}", subscription_type),
            };
            self.send_message(response).await?;
            error!(
                "Failed to subscribe client {} to {:?}",
                self.client_id, subscription_type
            );
        }

        Ok(())
    }

    /// 处理取消订阅请求
    async fn handle_unsubscribe(&self, subscription_type: SubscriptionType) -> Result<()> {
        let success = self
            .subscription_manager
            .unsubscribe(&self.client_id, &subscription_type);

        if success {
            let response = WebSocketMessage::Success {
                message: format!("Unsubscribed from {:?}", subscription_type),
            };
            self.send_message(response).await?;
            info!(
                "Client {} unsubscribed from {:?}",
                self.client_id, subscription_type
            );
        } else {
            let response = WebSocketMessage::Error {
                message: format!("Failed to unsubscribe from {:?}", subscription_type),
            };
            self.send_message(response).await?;
            warn!(
                "Failed to unsubscribe client {} from {:?}",
                self.client_id, subscription_type
            );
        }

        Ok(())
    }

    /// 处理Binance风格的订阅请求
    async fn handle_binance_subscribe(&mut self, msg: BinanceSubscribeMessage) -> Result<()> {
        let mut subscribed_streams = Vec::new();

        for stream in &msg.params {
            if let Some(subscription_type) = self.parse_stream_name(stream) {
                if self
                    .subscription_manager
                    .subscribe(&self.client_id, subscription_type.clone())
                {
                    self.subscribed_streams
                        .insert(stream.clone(), subscription_type);
                    subscribed_streams.push(stream.clone());
                    info!("Client {} subscribed to stream: {}", self.client_id, stream);
                }
            } else {
                warn!("Unknown stream name: {}", stream);
                self.send_binance_error(&format!("Unknown stream: {}", stream), msg.id)
                    .await?;
                return Ok(());
            }
        }

        // 发送成功响应
        let response = BinanceResponse {
            result: Some(serde_json::json!(null)),
            id: msg.id,
            error: None,
        };

        self.send_binance_message(serde_json::to_string(&response)?)
            .await?;
        Ok(())
    }

    /// 处理Binance风格的取消订阅请求
    async fn handle_binance_unsubscribe(&mut self, msg: BinanceUnsubscribeMessage) -> Result<()> {
        for stream in &msg.params {
            if let Some(subscription_type) = self.subscribed_streams.remove(stream) {
                self.subscription_manager
                    .unsubscribe(&self.client_id, &subscription_type);
                info!(
                    "Client {} unsubscribed from stream: {}",
                    self.client_id, stream
                );
            }
        }

        // 发送成功响应
        let response = BinanceResponse {
            result: Some(serde_json::json!(null)),
            id: msg.id,
            error: None,
        };

        self.send_binance_message(serde_json::to_string(&response)?)
            .await?;
        Ok(())
    }

    /// 解析流名称为订阅类型
    fn parse_stream_name(&self, stream: &str) -> Option<SubscriptionType> {
        if stream.ends_with("@bookTicker") {
            Some(SubscriptionType::BookTicker)
        } else if stream.ends_with("@aggTrade") {
            Some(SubscriptionType::AggTrade)
        } else if stream.contains("@depth") {
            // 支持 @depth5, @depth10, @depth20, @depth5@100ms 等格式
            Some(SubscriptionType::OrderBook)
        } else {
            None
        }
    }

    /// 发送Binance风格的错误消息
    async fn send_binance_error(&self, message: &str, id: Option<u64>) -> Result<()> {
        let response = BinanceResponse {
            result: None,
            id,
            error: Some(BinanceError {
                code: -1,
                msg: message.to_string(),
            }),
        };

        self.send_binance_message(serde_json::to_string(&response)?)
            .await
    }

    /// 发送Binance消息给客户端
    async fn send_binance_message(&self, message: String) -> Result<()> {
        self.message_tx
            .send(message)
            .await
            .map_err(|e| BacktestError::WebSocket(format!("Failed to send message: {}", e)))
    }

    /// 发送Pong消息
    async fn send_pong(&self, _payload: Vec<u8>) -> Result<()> {
        // 这里应该发送Pong帧，但由于我们使用的是文本消息通道
        // 我们发送一个Pong类型的WebSocket消息
        let response = WebSocketMessage::Pong;
        self.send_message(response).await
    }

    /// 发送错误消息
    async fn send_error(&self, error_message: String) -> Result<()> {
        let response = WebSocketMessage::Error {
            message: error_message,
        };
        self.send_message(response).await
    }

    /// 发送WebSocket消息
    async fn send_message(&self, message: WebSocketMessage) -> Result<()> {
        let json = serde_json::to_string(&message)
            .map_err(|e| BacktestError::WebSocket(format!("Failed to serialize message: {}", e)))?;

        self.message_tx
            .send(json)
            .await
            .map_err(|e| BacktestError::WebSocket(format!("Failed to send message: {}", e)))?;

        Ok(())
    }

    /// 处理客户端断开连接
    async fn handle_disconnect(&self) {
        info!("Handling disconnect for client {}", self.client_id);
        self.subscription_manager.remove_client(&self.client_id);
    }

    /// 获取客户端ID
    pub fn client_id(&self) -> ClientId {
        self.client_id
    }

    /// 获取客户端订阅信息
    pub fn get_subscriptions(&self) -> Option<std::collections::HashSet<SubscriptionType>> {
        self.subscription_manager
            .get_client_subscriptions(&self.client_id)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::sync::mpsc;

    #[tokio::test]
    async fn test_websocket_handler() {
        let subscription_manager = Arc::new(SubscriptionManager::new());
        let (tx, mut rx) = mpsc::channel(100);

        let client_id = subscription_manager.add_client(tx.clone());
        let mut handler = WebSocketHandler::new(client_id, subscription_manager.clone(), tx);

        // 测试订阅消息
        let subscribe_msg = WebSocketMessage::Subscribe {
            subscription: SubscriptionType::OrderBook,
        };
        let json = serde_json::to_string(&subscribe_msg).unwrap();
        let message = Message::Text(json);

        handler.handle_message(message).await.unwrap();

        // 检查是否收到响应
        let response = rx.recv().await.unwrap();
        let parsed: WebSocketMessage = serde_json::from_str(&response).unwrap();

        match parsed {
            WebSocketMessage::Success { message } => {
                assert!(message.contains("Subscribed"));
            }
            _ => panic!("Expected success message"),
        }
    }
}
