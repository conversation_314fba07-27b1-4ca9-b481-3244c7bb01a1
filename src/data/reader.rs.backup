use crate::config::ConfigManager;
use crate::types::{BookTicker, MarketData, OrderSide, Price};
use crate::{BacktestError, Result};
use chrono::{DateTime, Utc};
use std::io::BufRead;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::fs::File;
use tokio::io::{AsyncBufReadExt, BufReader};
use tokio::sync::{mpsc, Mutex};
use tokio::task::JoinHandle;
use tokio::time::Duration;
use tracing::{debug, error, info};

/// 数据读取器状态
#[derive(Debug, Clone, PartialEq)]
pub enum DataReaderStatus {
    /// 空闲状态
    Idle,
    /// 读取中
    Reading,
    /// 暂停状态
    Paused,
    /// 停止状态
    Stopped,
    /// 错误状态
    Error(String),
}

/// 数据读取器控制命令
#[derive(Debug, Clone)]
pub enum ReaderControlCommand {
    /// 开始读取
    Start,
    /// 暂停读取
    Pause,
    /// 恢复读取
    Resume,
    /// 停止读取
    Stop,
}

/// 数据读取器
/// 专注于从文件读取和解析数据，支持精确的状态控制
pub struct DataReader {
    data_path: PathBuf,
    status: Arc<Mutex<DataReaderStatus>>,
    control_rx: Arc<Mutex<mpsc::Receiver<ReaderControlCommand>>>,
    control_tx: mpsc::Sender<ReaderControlCommand>,
    handle: Option<JoinHandle<Result<()>>>,
}

impl DataReader {
    /// 创建新的数据读取器
    pub fn new() -> Result<Self> {
        let config = ConfigManager::get()?;
        let (control_tx, control_rx) = mpsc::channel(32);

        Ok(Self {
            data_path: config.data_paths.root,
            status: Arc::new(Mutex::new(DataReaderStatus::Idle)),
            control_rx: Arc::new(Mutex::new(control_rx)),
            control_tx,
            handle: None,
        })
    }

    /// 从指定路径创建数据读取器
    pub fn with_path(data_path: PathBuf) -> Self {
        let (control_tx, control_rx) = mpsc::channel(32);
        Self {
            data_path,
            status: Arc::new(Mutex::new(DataReaderStatus::Idle)),
            control_rx: Arc::new(Mutex::new(control_rx)),
            control_tx,
            handle: None,
        }
    }

    /// 获取当前状态
    pub async fn get_status(&self) -> DataReaderStatus {
        self.status.lock().await.clone()
    }

    /// 启动读取器
    pub async fn start(&self) -> Result<()> {
        *self.status.lock().await = DataReaderStatus::Reading;
        info!("Data reading started");
        Ok(())
    }

    /// 暂停读取器
    pub async fn pause(&self) -> Result<()> {
        *self.status.lock().await = DataReaderStatus::Paused;
        info!("Data reading paused");
        Ok(())
    }

    /// 恢复读取器
    pub async fn resume(&self) -> Result<()> {
        *self.status.lock().await = DataReaderStatus::Reading;
        info!("Data reading resumed");
        Ok(())
    }

    /// 停止读取器
    pub async fn stop(&self) -> Result<()> {
        *self.status.lock().await = DataReaderStatus::Stopped;
        info!("Data reading stopped");
        Ok(())
    }

    /// 开始读取数据（支持控制命令）
    /// 这是主要的数据读取方法，支持暂停、恢复、停止等控制
    pub async fn prepare_reading(&mut self, output_tx: mpsc::Sender<MarketData>) -> Result<()> {
        info!("Prepare data reading from path: {:?}", self.data_path);

        let config = ConfigManager::get()?;

        // 根据数据源类型选择不同的读取方法
        match config.data_source_type {
            crate::config::DataSourceType::BinanceOfficial => {
                let _ = self
                    .read_bookticker_data_with_control(
                        output_tx,
                        config.start_time,
                        config.end_time,
                    )
                    .await;
            }
            crate::config::DataSourceType::Tardis => {
                // 同时读取quotes和trades数据，根据订阅需求分发
                let _ = self
                    .read_combined_tardis_data_with_control(
                        output_tx,
                        config.start_time,
                        config.end_time,
                    )
                    .await;
            }
        }

        info!("Data reading prepared");
        Ok(())
    }

    async fn read_bookticker_data_with_control(
        &mut self,
        output_tx: mpsc::Sender<MarketData>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<()> {
        // 查找CSV格式的BookTicker文件
        let bookticker_files = self.find_bookticker_files().await?;

        if bookticker_files.is_empty() {
            error!("No BookTicker CSV files found in: {:?}", self.data_path);
            return Ok(());
        }

        let mut line_buffer = Vec::new();
        let mut current_line_index = 0;

        // 预加载所有文件的数据
        for file_path in &bookticker_files {
            info!("Loading BookTicker data from: {:?}", file_path);

            let file = File::open(file_path).await.map_err(|e| {
                BacktestError::Data(format!("Failed to open BookTicker file: {}", e))
            })?;

            let reader = BufReader::new(file);
            let mut lines = reader.lines();

            // 跳过CSV头部
            if let Some(_header) = lines
                .next_line()
                .await
                .map_err(|e| BacktestError::Data(format!("Failed to read header: {}", e)))?
            {
                debug!("Skipped CSV header for file: {:?}", file_path);
            }

            // 读取所有行到缓冲区
            while let Some(line) = lines
                .next_line()
                .await
                .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
            {
                line_buffer.push(line);
            }
        }

        info!("Loaded {} lines of BookTicker data", line_buffer.len());

        let status = self.status.clone();
        self.handle = Some(tokio::spawn(async move {
            loop {
                let s = status.lock().await.clone();
                match s {
                    DataReaderStatus::Reading => {
                        if current_line_index < line_buffer.len() {
                            let line = &line_buffer[current_line_index];
                            match Self::parse_bookticker_csv_line(line) {
                                Ok(Some(market_data)) => {
                                    if let Err(e) = output_tx.send(market_data).await {
                                        error!("Failed to send BookTicker data: {}", e);
                                        return Err(BacktestError::Data(format!(
                                            "Failed to send data: {}",
                                            e
                                        )));
                                    }
                                }
                                Ok(None) => {
                                    info!("Skipped empty line {}", current_line_index + 1);
                                }
                                Err(e) => {
                                    error!(
                                        "Failed to parse BookTicker line {}: {}",
                                        current_line_index + 1,
                                        e
                                    );
                                }
                            }

                            current_line_index += 1;
                            if current_line_index % 1000000 == 0 {
                                info!(
                                    "BookTicker process: {}/{}",
                                    current_line_index,
                                    line_buffer.len()
                                );
                            }
                        } else {
                            // 所有数据读取完成
                            info!(
                                "All BookTicker data has been read ({} lines)",
                                line_buffer.len()
                            );
                            *status.lock().await = DataReaderStatus::Idle;
                            return Ok(());
                        }
                    }
                    DataReaderStatus::Idle => {
                        tokio::time::sleep(Duration::from_millis(2000)).await;
                    }
                    DataReaderStatus::Stopped => {
                        current_line_index = 0;
                        tokio::time::sleep(Duration::from_millis(2000)).await;
                    }
                    DataReaderStatus::Paused => {
                        tokio::time::sleep(Duration::from_millis(2000)).await;
                    }
                    DataReaderStatus::Error(ref e) => {
                        error!("Data reading error: {}", e);
                        return Err(BacktestError::Data(e.clone()));
                    }
                }
            }
        }));
        Ok(())
    }

    /// 查找BookTicker CSV文件
    async fn find_bookticker_files(&self) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();

        // 获取配置以确定BookTicker数据路径
        let config = ConfigManager::get()?;
        let bookticker_path = config.data_paths.get_bookticker_path();
        info!("Searching for BookTicker files in: {:?}", bookticker_path);

        // 读取BookTicker数据目录
        let mut dir = tokio::fs::read_dir(&bookticker_path).await.map_err(|e| {
            BacktestError::Data(format!("Failed to read BookTicker data directory: {}", e))
        })?;

        while let Some(entry) = dir
            .next_entry()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory entry: {}", e)))?
        {
            let path = entry.path();
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                // 查找包含"bookTicker"的CSV文件
                if file_name.contains("bookTicker") && file_name.ends_with(".csv") {
                    files.push(path);
                }
            }
        }

        // 按文件名排序以确保一致的处理顺序
        files.sort();

        Ok(files)
    }

    /// 查找深度数据CSV文件
    pub async fn find_depth_files(&self) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();

        let config = ConfigManager::get()?;
        let depth_path = config.data_paths.get_depth_path();

        let mut dir = tokio::fs::read_dir(&depth_path).await.map_err(|e| {
            BacktestError::Data(format!("Failed to read depth data directory: {}", e))
        })?;

        while let Some(entry) = dir
            .next_entry()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory entry: {}", e)))?
        {
            let path = entry.path();
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                // 查找包含"depth"的CSV文件
                if file_name.contains("depth") && file_name.ends_with(".csv") {
                    files.push(path);
                }
            }
        }

        files.sort();
        Ok(files)
    }

    /// 查找订单簿数据CSV文件
    pub async fn find_orderbook_files(&self) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();

        let config = ConfigManager::get()?;
        let orderbook_path = config.data_paths.get_orderbook_path();

        let mut dir = tokio::fs::read_dir(&orderbook_path).await.map_err(|e| {
            BacktestError::Data(format!("Failed to read orderbook data directory: {}", e))
        })?;

        while let Some(entry) = dir
            .next_entry()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory entry: {}", e)))?
        {
            let path = entry.path();
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                // 查找包含"orderbook"的CSV文件
                if file_name.contains("orderbook") && file_name.ends_with(".csv") {
                    files.push(path);
                }
            }
        }

        files.sort();
        Ok(files)
    }

    /// 解析BookTicker CSV行数据
    fn parse_bookticker_csv_line(line: &str) -> Result<Option<MarketData>> {
        if line.trim().is_empty() {
            return Ok(None);
        }

        let fields: Vec<&str> = line.split(',').collect();
        if fields.len() != 7 {
            return Err(BacktestError::Data(format!(
                "Invalid BookTicker CSV format: expected 7 fields, got {}",
                fields.len()
            )));
        }

        // CSV格式: update_id,best_bid_price,best_bid_qty,best_ask_price,best_ask_qty,transaction_time,event_time
        let update_id: u64 = fields[0]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid update_id: {}", e)))?;

        let best_bid_price: f64 = fields[1]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid best_bid_price: {}", e)))?;

        let best_bid_qty: f64 = fields[2]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid best_bid_qty: {}", e)))?;

        let best_ask_price: f64 = fields[3]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid best_ask_price: {}", e)))?;

        let best_ask_qty: f64 = fields[4]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid best_ask_qty: {}", e)))?;

        let transaction_time: u64 = fields[5]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid transaction_time: {}", e)))?;

        let event_time: u64 = fields[6]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid event_time: {}", e)))?;

        let bookticker = BookTicker::new(
            update_id,
            Price::new(best_bid_price),
            best_bid_qty,
            Price::new(best_ask_price),
            best_ask_qty,
            transaction_time,
            event_time,
        );

        Ok(Some(MarketData::BookTicker(bookticker)))
    }

    /// 读取Quotes数据（支持控制命令）
    async fn read_quotes_data_with_control(
        &mut self,
        output_tx: mpsc::Sender<MarketData>,
        _start_time: DateTime<Utc>,
        _end_time: DateTime<Utc>,
    ) -> Result<()> {
        // 查找Quotes CSV文件
        let quotes_files = self.find_quotes_files().await?;

        if quotes_files.is_empty() {
            error!("No Quotes CSV files found in quotes directory");
            return Ok(());
        }

        let mut line_buffer = Vec::new();
        let mut current_line_index = 0;

        // 预加载所有文件的数据
        for file_path in &quotes_files {
            info!("Loading Quotes data from: {:?}", file_path);

            // 处理压缩文件
            if file_path.extension().and_then(|s| s.to_str()) == Some("gz") {
                let file = std::fs::File::open(file_path).map_err(|e| {
                    BacktestError::Data(format!("Failed to open Quotes file: {}", e))
                })?;

                let decoder = flate2::read::GzDecoder::new(file);
                let reader = std::io::BufReader::new(decoder);

                // 跳过CSV头部
                let mut lines = reader.lines();
                if let Some(_header) = lines.next() {
                    debug!("Skipped CSV header for file: {:?}", file_path);
                }

                // 读取所有行到缓冲区
                for line in lines {
                    let line = line
                        .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?;
                    line_buffer.push(line);
                }
            } else {
                // 处理未压缩文件
                let file = tokio::fs::File::open(file_path).await.map_err(|e| {
                    BacktestError::Data(format!("Failed to open Quotes file: {}", e))
                })?;

                let reader = BufReader::new(file);
                let mut lines = reader.lines();

                // 跳过CSV头部
                if let Some(_header) = lines
                    .next_line()
                    .await
                    .map_err(|e| BacktestError::Data(format!("Failed to read header: {}", e)))?
                {
                    debug!("Skipped CSV header for file: {:?}", file_path);
                }

                // 读取所有行到缓冲区
                while let Some(line) = lines
                    .next_line()
                    .await
                    .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
                {
                    line_buffer.push(line);
                }
            }
        }

        info!("Loaded {} lines of Quotes data", line_buffer.len());

        let status = self.status.clone();
        self.handle = Some(tokio::spawn(async move {
            loop {
                let s = status.lock().await.clone();
                match s {
                    DataReaderStatus::Reading => {
                        if current_line_index < line_buffer.len() {
                            let line = &line_buffer[current_line_index];
                            match Self::parse_quotes_csv_line(line) {
                                Ok(Some(market_data)) => {
                                    if let Err(e) = output_tx.send(market_data).await {
                                        error!("Failed to send Quotes data: {}", e);
                                        return Err(BacktestError::Data(format!(
                                            "Failed to send data: {}",
                                            e
                                        )));
                                    }
                                }
                                Ok(None) => {
                                    info!("Skipped empty line {}", current_line_index + 1);
                                }
                                Err(e) => {
                                    error!(
                                        "Failed to parse Quotes line {}: {}",
                                        current_line_index + 1,
                                        e
                                    );
                                }
                            }
                            current_line_index += 1;
                            tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                        } else {
                            info!("Finished reading all Quotes data");
                            return Ok(());
                        }
                    }
                    DataReaderStatus::Paused => {
                        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    }
                    DataReaderStatus::Stopped => {
                        info!("Data reading stopped");
                        return Ok(());
                    }
                    DataReaderStatus::Idle => {
                        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    }
                    DataReaderStatus::Error(ref e) => {
                        error!("Data reading error: {}", e);
                        return Err(BacktestError::Data(e.clone()));
                    }
                }
            }
        }));

        Ok(())
    }

    /// 查找Quotes数据CSV文件
    pub async fn find_quotes_files(&self) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();

        let config = ConfigManager::get()?;
        let quotes_path = config.data_paths.get_quotes_path();

        let mut dir = tokio::fs::read_dir(&quotes_path).await.map_err(|e| {
            BacktestError::Data(format!("Failed to read quotes data directory: {}", e))
        })?;

        while let Some(entry) = dir
            .next_entry()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory entry: {}", e)))?
        {
            let path = entry.path();
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                // 查找CSV或压缩的CSV文件（支持按日期命名的格式）
                if (file_name.ends_with(".csv") || file_name.ends_with(".csv.gz"))
                    && file_name.contains("BTCUSDT")
                {
                    files.push(path);
                }
            }
        }

        files.sort();
        Ok(files)
    }

    /// 查找Trades数据CSV文件（重新实现以支持新格式）
    pub async fn find_new_trades_files(&self) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();

        let config = ConfigManager::get()?;
        let trades_path = config.data_paths.get_trades_path();

        let mut dir = tokio::fs::read_dir(&trades_path).await.map_err(|e| {
            BacktestError::Data(format!("Failed to read trades data directory: {}", e))
        })?;

        while let Some(entry) = dir
            .next_entry()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory entry: {}", e)))?
        {
            let path = entry.path();
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                // 查找CSV或压缩的CSV文件（支持按日期命名的格式）
                if (file_name.ends_with(".csv") || file_name.ends_with(".csv.gz"))
                    && file_name.contains("BTCUSDT")
                {
                    files.push(path);
                }
            }
        }

        files.sort();
        Ok(files)
    }

    /// 解析Quotes CSV行数据（直接转换为BBO）
    fn parse_quotes_csv_line(line: &str) -> Result<Option<MarketData>> {
        if line.trim().is_empty() {
            return Ok(None);
        }

        let fields: Vec<&str> = line.split(',').collect();
        if fields.len() != 8 {
            return Err(BacktestError::Data(format!(
                "Invalid Quotes CSV format: expected 8 fields, got {}",
                fields.len()
            )));
        }

        // CSV格式: exchange,symbol,timestamp,local_timestamp,ask_amount,ask_price,bid_price,bid_amount
        let _exchange = fields[0]; // 暂时不使用
        let _symbol = fields[1]; // 暂时不使用

        let _timestamp: u64 = fields[2]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid timestamp: {}", e)))?;

        let _local_timestamp: u64 = fields[3]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid local_timestamp: {}", e)))?;

        let ask_amount: f64 = fields[4]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid ask_amount: {}", e)))?;

        let ask_price: f64 = fields[5]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid ask_price: {}", e)))?;

        let bid_price: f64 = fields[6]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid bid_price: {}", e)))?;

        let bid_amount: f64 = fields[7]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid bid_amount: {}", e)))?;

        // 直接创建BBO数据
        let bbo = crate::types::Bbo {
            update_id: 0, // Quotes数据没有update_id，使用0
            bid_price: Price::new(bid_price),
            bid_quantity: bid_amount,
            ask_price: Price::new(ask_price),
            ask_quantity: ask_amount,
        };

        Ok(Some(MarketData::Bbo(bbo)))
    }

    /// 解析Trades CSV行数据
    fn parse_trades_csv_line(line: &str) -> Result<Option<MarketData>> {
        if line.trim().is_empty() {
            return Ok(None);
        }

        let fields: Vec<&str> = line.split(',').collect();
        if fields.len() != 8 {
            return Err(BacktestError::Data(format!(
                "Invalid Trades CSV format: expected 8 fields, got {}",
                fields.len()
            )));
        }

        // CSV格式: exchange,symbol,timestamp,local_timestamp,id,side,price,amount
        let exchange = fields[0].to_string();
        let symbol = fields[1].to_string();

        let timestamp: u64 = fields[2]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid timestamp: {}", e)))?;

        let local_timestamp: u64 = fields[3]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid local_timestamp: {}", e)))?;

        let id = fields[4].to_string();

        let side = match fields[5] {
            "buy" => OrderSide::Buy,
            "sell" => OrderSide::Sell,
            _ => return Err(BacktestError::Data(format!("Invalid side: {}", fields[5]))),
        };

        let price: f64 = fields[6]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid price: {}", e)))?;

        let amount: f64 = fields[7]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid amount: {}", e)))?;

        let trade_data = crate::types::TradeData {
            exchange,
            symbol,
            timestamp,
            local_timestamp,
            id,
            side,
            price: Price::new(price),
            amount,
        };

        Ok(Some(MarketData::TradeData(trade_data)))
    }

    /// 获取MarketData的时间戳（用于排序）
    fn get_market_data_timestamp(&self, data: &MarketData) -> i64 {
        match data {
            MarketData::Bbo(_) => 0, // Bbo没有时间戳，使用默认值
            MarketData::TradeData(trade) => trade.timestamp as i64,
            MarketData::BookTicker(ticker) => ticker.event_time as i64,
            _ => 0, // 其他类型默认时间戳
        }
    }

    /// 读取Trades数据（支持控制命令）
    async fn read_trades_data_with_control(
        &mut self,
        output_tx: mpsc::Sender<MarketData>,
        _start_time: DateTime<Utc>,
        _end_time: DateTime<Utc>,
    ) -> Result<()> {
        // 查找Trades CSV文件
        let trades_files = self.find_new_trades_files().await?;

        if trades_files.is_empty() {
            error!("No Trades CSV files found in trades directory");
            return Ok(());
        }

        info!(
            "Starting lazy loading of {} Trades files",
            trades_files.len()
        );

        let status = self.status.clone();
        let control_rx = self.control_rx.clone();

        self.handle = Some(tokio::spawn(async move {
            // 逐个文件处理，不预加载到内存
            for file_path in &trades_files {
                info!("Processing Trades data from: {:?}", file_path);

                // 检查状态
                let s = status.lock().await.clone();
                match s {
                    DataReaderStatus::Stopped => {
                        info!("Data reading stopped");
                        return Ok(());
                    }
                    DataReaderStatus::Error(ref e) => {
                        error!("Data reading error: {}", e);
                        return Err(BacktestError::Data(e.clone()));
                    }
                    _ => {}
                }

                // 处理压缩文件
                if file_path.extension().and_then(|s| s.to_str()) == Some("gz") {
                    let file = std::fs::File::open(file_path).map_err(|e| {
                        BacktestError::Data(format!("Failed to open Trades file: {}", e))
                    })?;

                    let decoder = flate2::read::GzDecoder::new(file);
                    let reader = std::io::BufReader::new(decoder);

                    // 跳过CSV头部
                    let mut lines = reader.lines();
                    if let Some(_header) = lines.next() {
                        debug!("Skipped CSV header for file: {:?}", file_path);
                    }

                    // 逐行处理
                    for line in lines {
                        let line = line.map_err(|e| {
                            BacktestError::Data(format!("Failed to read line: {}", e))
                        })?;

                        // 检查控制命令
                        if let Ok(command) = control_rx.lock().await.try_recv() {
                            match command {
                                ReaderControlCommand::Stop => {
                                    info!("Received stop command, stopping data reading");
                                    return Ok(());
                                }
                                ReaderControlCommand::Pause => {
                                    info!("Received pause command, pausing data reading");
                                    // 等待恢复命令
                                    loop {
                                        if let Some(ReaderControlCommand::Resume) =
                                            control_rx.lock().await.recv().await
                                        {
                                            info!("Received resume command, resuming data reading");
                                            break;
                                        }
                                    }
                                }
                                _ => {}
                            }
                        }

                        // 解析并立即发送数据
                        if !line.trim().is_empty() {
                            match Self::parse_trades_csv_line(&line) {
                                Ok(Some(market_data)) => {
                                    if let Err(e) = output_tx.send(market_data).await {
                                        error!("Failed to send Trades data: {}", e);
                                        return Err(BacktestError::Data(format!(
                                            "Failed to send data: {}",
                                            e
                                        )));
                                    }
                                }
                                Ok(None) => {
                                    debug!("Skipped empty line");
                                }
                                Err(e) => {
                                    error!("Failed to parse Trades line: {}", e);
                                }
                            }

                            // 控制发送速度
                            tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                        }
                    }
                } else {
                    // 处理未压缩文件
                    let file = tokio::fs::File::open(file_path).await.map_err(|e| {
                        BacktestError::Data(format!("Failed to open Trades file: {}", e))
                    })?;

                    let reader = BufReader::new(file);
                    let mut lines = reader.lines();

                    // 跳过CSV头部
                    if let Some(_header) = lines
                        .next_line()
                        .await
                        .map_err(|e| BacktestError::Data(format!("Failed to read header: {}", e)))?
                    {
                        debug!("Skipped CSV header for file: {:?}", file_path);
                    }

                    // 逐行处理
                    while let Some(line) = lines
                        .next_line()
                        .await
                        .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
                    {
                        // 检查控制命令
                        if let Ok(command) = control_rx.lock().await.try_recv() {
                            match command {
                                ReaderControlCommand::Stop => {
                                    info!("Received stop command, stopping data reading");
                                    return Ok(());
                                }
                                ReaderControlCommand::Pause => {
                                    info!("Received pause command, pausing data reading");
                                    // 等待恢复命令
                                    loop {
                                        if let Some(ReaderControlCommand::Resume) =
                                            control_rx.lock().await.recv().await
                                        {
                                            info!("Received resume command, resuming data reading");
                                            break;
                                        }
                                    }
                                }
                                _ => {}
                            }
                        }

                        // 解析并立即发送数据
                        if !line.trim().is_empty() {
                            match Self::parse_trades_csv_line(&line) {
                                Ok(Some(market_data)) => {
                                    if let Err(e) = output_tx.send(market_data).await {
                                        error!("Failed to send Trades data: {}", e);
                                        return Err(BacktestError::Data(format!(
                                            "Failed to send data: {}",
                                            e
                                        )));
                                    }
                                }
                                Ok(None) => {
                                    debug!("Skipped empty line");
                                }
                                Err(e) => {
                                    error!("Failed to parse Trades line: {}", e);
                                }
                            }

                            // 控制发送速度
                            tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                        }
                    }
                }

                info!("Completed processing file: {:?}", file_path);
            }

            info!("All Trades data files processed successfully");
            Ok(())
        }));

        Ok(())
    }

    /// 同时读取Tardis的quotes和trades数据并支持控制命令 - 并行协程+Lazy Loading版本
    async fn read_combined_tardis_data_with_control(
        &mut self,
        output_tx: mpsc::Sender<MarketData>,
        _start_time: DateTime<Utc>,
        _end_time: DateTime<Utc>,
    ) -> Result<()> {
        info!("Starting combined Tardis data reading with parallel coroutines and lazy loading");

        // 查找文件路径（只收集路径，不加载内容）
        let quotes_files = self.find_quotes_files().await?;
        let trades_files = self.find_new_trades_files().await?;

        info!(
            "Found {} quotes files and {} trades files for lazy loading",
            quotes_files.len(),
            trades_files.len()
        );

        let status = self.status.clone();
        let control_rx = self.control_rx.clone();

        self.handle = Some(tokio::spawn(async move {
            // 为不同数据类型创建独立的协程
            let quotes_output_tx = output_tx.clone();
            let trades_output_tx = output_tx.clone();
            let quotes_status = status.clone();
            let trades_status = status.clone();
            let quotes_control_rx = control_rx.clone();
            let trades_control_rx = control_rx.clone();

            // 启动quotes协程 - 处理BBO数据
            let quotes_task = tokio::spawn(Self::process_quotes_files_lazy(
                quotes_files,
                quotes_output_tx,
                quotes_status,
                quotes_control_rx,
            ));

            // 启动trades协程 - 处理交易数据
            let trades_task = tokio::spawn(Self::process_trades_files_lazy(
                trades_files,
                trades_output_tx,
                trades_status,
                trades_control_rx,
            ));

            // 等待两个协程完成
            let (quotes_result, trades_result) = tokio::join!(quotes_task, trades_task);

            // 检查结果
            match quotes_result {
                Ok(Ok(())) => info!("Quotes coroutine completed successfully"),
                Ok(Err(e)) => {
                    error!("Quotes processing failed: {}", e);
                    return Err(e);
                }
                Err(e) => {
                    error!("Quotes coroutine panicked: {}", e);
                    return Err(BacktestError::Data(format!("Quotes coroutine panicked: {}", e)));
                }
            }

            match trades_result {
                Ok(Ok(())) => info!("Trades coroutine completed successfully"),
                Ok(Err(e)) => {
                    error!("Trades processing failed: {}", e);
                    return Err(e);
                }
                Err(e) => {
                    error!("Trades coroutine panicked: {}", e);
                    return Err(BacktestError::Data(format!("Trades coroutine panicked: {}", e)));
                }
            }

            info!("All coroutines completed successfully with lazy loading");
            Ok(())
        }));

        Ok(())
    }

    /// 独立协程：处理quotes文件（BBO数据）- Lazy Loading
    async fn process_quotes_files_lazy(
        quotes_files: Vec<std::path::PathBuf>,
        output_tx: mpsc::Sender<MarketData>,
        status: Arc<Mutex<DataReaderStatus>>,
        control_rx: Arc<Mutex<mpsc::Receiver<ReaderControlCommand>>>,
    ) -> Result<()> {
        if quotes_files.is_empty() {
            info!("No quotes files to process");
            return Ok(());
        }

        info!("Starting quotes processing coroutine for {} files with lazy loading", quotes_files.len());

        for file_path in quotes_files {
            // 检查状态
            let s = status.lock().await.clone();
            match s {
                DataReaderStatus::Stopped => {
                    info!("Quotes processing stopped");
                    return Ok(());
                }
                DataReaderStatus::Error(ref e) => {
                    error!("Quotes processing error: {}", e);
                    return Err(BacktestError::Data(e.clone()));
                }
                _ => {}
            }

            info!("Lazy loading quotes file: {:?}", file_path);

            // Lazy loading: 只在需要时才打开和解压文件
            if file_path.extension().and_then(|s| s.to_str()) == Some("gz") {
                // 处理压缩文件
                let file = std::fs::File::open(&file_path).map_err(|e| {
                    BacktestError::Data(format!("Failed to open quotes file: {}", e))
                })?;

                let decoder = flate2::read::GzDecoder::new(file);
                let reader = std::io::BufReader::new(decoder);
                let mut lines = reader.lines();

                // 跳过CSV头部
                if let Some(_header) = lines.next() {
                    debug!("Skipped CSV header for quotes file: {:?}", file_path);
                }

                // 逐行处理quotes数据
                for line in lines {
                    let line = line.map_err(|e| {
                        BacktestError::Data(format!("Failed to read line: {}", e))
                    })?;

                    // 检查控制命令
                    if let Ok(command) = control_rx.lock().await.try_recv() {
                        match command {
                            ReaderControlCommand::Stop => {
                                info!("Received stop command, stopping quotes processing");
                                return Ok(());
                            }
                            ReaderControlCommand::Pause => {
                                info!("Received pause command, pausing quotes processing");
                                loop {
                                    if let Some(ReaderControlCommand::Resume) =
                                        control_rx.lock().await.recv().await
                                    {
                                        info!("Received resume command, resuming quotes processing");
                                        break;
                                    }
                                }
                            }
                            _ => {}
                        }
                    }

                    // 解析并发送quotes数据
                    if !line.trim().is_empty() {
                        match Self::parse_quotes_csv_line(&line) {
                            Ok(Some(market_data)) => {
                                if let Err(e) = output_tx.send(market_data).await {
                                    error!("Failed to send quotes data: {}", e);
                                    return Err(BacktestError::Data(format!(
                                        "Failed to send quotes data: {}",
                                        e
                                    )));
                                }
                            }
                            Ok(None) => {
                                debug!("Skipped empty quotes line");
                            }
                            Err(e) => {
                                error!("Failed to parse quotes line: {}", e);
                            }
                        }

                        // 控制发送速度
                        tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                    }
                }
            } else {
                // 处理未压缩文件
                let file = tokio::fs::File::open(&file_path).await.map_err(|e| {
                    BacktestError::Data(format!("Failed to open quotes file: {}", e))
                })?;

                let reader = BufReader::new(file);
                let mut lines = reader.lines();

                // 跳过CSV头部
                if let Some(_header) = lines.next_line().await.map_err(|e| {
                    BacktestError::Data(format!("Failed to read header: {}", e))
                })? {
                    debug!("Skipped CSV header for quotes file: {:?}", file_path);
                }

                // 逐行处理quotes数据
                while let Some(line) = lines.next_line().await.map_err(|e| {
                    BacktestError::Data(format!("Failed to read line: {}", e))
                })? {
                    // 检查控制命令
                    if let Ok(command) = control_rx.lock().await.try_recv() {
                        match command {
                            ReaderControlCommand::Stop => {
                                info!("Received stop command, stopping quotes processing");
                                return Ok(());
                            }
                            ReaderControlCommand::Pause => {
                                info!("Received pause command, pausing quotes processing");
                                loop {
                                    if let Some(ReaderControlCommand::Resume) =
                                        control_rx.lock().await.recv().await
                                    {
                                        info!("Received resume command, resuming quotes processing");
                                        break;
                                    }
                                }
                            }
                            _ => {}
                        }
                    }

                    // 解析并发送quotes数据
                    if !line.trim().is_empty() {
                        match Self::parse_quotes_csv_line(&line) {
                            Ok(Some(market_data)) => {
                                if let Err(e) = output_tx.send(market_data).await {
                                    error!("Failed to send quotes data: {}", e);
                                    return Err(BacktestError::Data(format!(
                                        "Failed to send quotes data: {}",
                                        e
                                    )));
                                }
                            }
                            Ok(None) => {
                                debug!("Skipped empty quotes line");
                            }
                            Err(e) => {
                                error!("Failed to parse quotes line: {}", e);
                            }
                        }

                        // 控制发送速度
                        tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                    }
                }
            }

            info!("Completed lazy loading quotes file: {:?}", file_path);
        }

        info!("Quotes processing coroutine completed all files");
        Ok(())
    }

    /// 独立协程：处理trades文件（交易数据）- Lazy Loading
    async fn process_trades_files_lazy(
        trades_files: Vec<std::path::PathBuf>,
        output_tx: mpsc::Sender<MarketData>,
        status: Arc<Mutex<DataReaderStatus>>,
        control_rx: Arc<Mutex<mpsc::Receiver<ReaderControlCommand>>>,
    ) -> Result<()> {
        if trades_files.is_empty() {
            info!("No trades files to process");
            return Ok(());
        }

        info!("Starting trades processing coroutine for {} files with lazy loading", trades_files.len());

        for file_path in trades_files {
            // 检查状态
            let s = status.lock().await.clone();
            match s {
                DataReaderStatus::Stopped => {
                    info!("Trades processing stopped");
                    return Ok(());
                }
                DataReaderStatus::Error(ref e) => {
                    error!("Trades processing error: {}", e);
                    return Err(BacktestError::Data(e.clone()));
                }
                _ => {}
            }

            info!("Lazy loading trades file: {:?}", file_path);

            // Lazy loading: 只在需要时才打开和解压文件
            if file_path.extension().and_then(|s| s.to_str()) == Some("gz") {
                // 处理压缩文件
                let file = std::fs::File::open(&file_path).map_err(|e| {
                    BacktestError::Data(format!("Failed to open trades file: {}", e))
                })?;

                let decoder = flate2::read::GzDecoder::new(file);
                let reader = std::io::BufReader::new(decoder);
                let mut lines = reader.lines();

                // 跳过CSV头部
                if let Some(_header) = lines.next() {
                    debug!("Skipped CSV header for trades file: {:?}", file_path);
                }

                // 逐行处理trades数据
                for line in lines {
                    let line = line.map_err(|e| {
                        BacktestError::Data(format!("Failed to read line: {}", e))
                    })?;

                    // 检查控制命令
                    if let Ok(command) = control_rx.lock().await.try_recv() {
                        match command {
                            ReaderControlCommand::Stop => {
                                info!("Received stop command, stopping trades processing");
                                return Ok(());
                            }
                            ReaderControlCommand::Pause => {
                                info!("Received pause command, pausing trades processing");
                                loop {
                                    if let Some(ReaderControlCommand::Resume) =
                                        control_rx.lock().await.recv().await
                                    {
                                        info!("Received resume command, resuming trades processing");
                                        break;
                                    }
                                }
                            }
                            _ => {}
                        }
                    }

                    // 解析并发送trades数据
                    if !line.trim().is_empty() {
                        match Self::parse_trades_csv_line(&line) {
                            Ok(Some(market_data)) => {
                                if let Err(e) = output_tx.send(market_data).await {
                                    error!("Failed to send trades data: {}", e);
                                    return Err(BacktestError::Data(format!(
                                        "Failed to send trades data: {}",
                                        e
                                    )));
                                }
                            }
                            Ok(None) => {
                                debug!("Skipped empty trades line");
                            }
                            Err(e) => {
                                error!("Failed to parse trades line: {}", e);
                            }
                        }

                        // 控制发送速度
                        tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                    }
                }
            } else {
                // 处理未压缩文件
                let file = tokio::fs::File::open(&file_path).await.map_err(|e| {
                    BacktestError::Data(format!("Failed to open trades file: {}", e))
                })?;

                let reader = BufReader::new(file);
                let mut lines = reader.lines();

                // 跳过CSV头部
                if let Some(_header) = lines.next_line().await.map_err(|e| {
                    BacktestError::Data(format!("Failed to read header: {}", e))
                })? {
                    debug!("Skipped CSV header for trades file: {:?}", file_path);
                }

                // 逐行处理trades数据
                while let Some(line) = lines.next_line().await.map_err(|e| {
                    BacktestError::Data(format!("Failed to read line: {}", e))
                })? {
                    // 检查控制命令
                    if let Ok(command) = control_rx.lock().await.try_recv() {
                        match command {
                            ReaderControlCommand::Stop => {
                                info!("Received stop command, stopping trades processing");
                                return Ok(());
                            }
                            ReaderControlCommand::Pause => {
                                info!("Received pause command, pausing trades processing");
                                loop {
                                    if let Some(ReaderControlCommand::Resume) =
                                        control_rx.lock().await.recv().await
                                    {
                                        info!("Received resume command, resuming trades processing");
                                        break;
                                    }
                                }
                            }
                            _ => {}
                        }
                    }

                    // 解析并发送trades数据
                    if !line.trim().is_empty() {
                        match Self::parse_trades_csv_line(&line) {
                            Ok(Some(market_data)) => {
                                if let Err(e) = output_tx.send(market_data).await {
                                    error!("Failed to send trades data: {}", e);
                                    return Err(BacktestError::Data(format!(
                                        "Failed to send trades data: {}",
                                        e
                                    )));
                                }
                            }
                            Ok(None) => {
                                debug!("Skipped empty trades line");
                            }
                            Err(e) => {
                                error!("Failed to parse trades line: {}", e);
                            }
                        }

                        // 控制发送速度
                        tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                    }
                }
            }

            info!("Completed lazy loading trades file: {:?}", file_path);
        }

        info!("Trades processing coroutine completed all files");
        Ok(())
    }
}
                                BacktestError::Data(format!("Failed to open Quotes file: {}", e))
                            })?;

                            let decoder = flate2::read::GzDecoder::new(file);
                            let reader = std::io::BufReader::new(decoder);

                            // 跳过CSV头部
                            let mut lines = reader.lines();
                            if let Some(_header) = lines.next() {
                                debug!("Skipped CSV header for quotes file: {:?}", file_path);
                            }

                            // 逐行处理quotes数据
                            for line in lines {
                                let line = line.map_err(|e| {
                                    BacktestError::Data(format!("Failed to read line: {}", e))
                                })?;

                                // 检查控制命令
                                if let Ok(command) = quotes_control_rx.lock().await.try_recv() {
                                    match command {
                                        ReaderControlCommand::Stop => {
                                            info!(
                                                "Received stop command, stopping quotes processing"
                                            );
                                            return Ok(());
                                        }
                                        ReaderControlCommand::Pause => {
                                            info!(
                                                "Received pause command, pausing quotes processing"
                                            );
                                            // 等待恢复命令
                                            loop {
                                                if let Some(ReaderControlCommand::Resume) =
                                                    quotes_control_rx.lock().await.recv().await
                                                {
                                                    info!("Received resume command, resuming quotes processing");
                                                    break;
                                                }
                                            }
                                        }
                                        _ => {}
                                    }
                                }

                                // 解析并立即发送quotes数据
                                if !line.trim().is_empty() {
                                    match Self::parse_quotes_csv_line(&line) {
                                        Ok(Some(market_data)) => {
                                            if let Err(e) = quotes_output_tx.send(market_data).await
                                            {
                                                error!("Failed to send Quotes data: {}", e);
                                                return Err(BacktestError::Data(format!(
                                                    "Failed to send data: {}",
                                                    e
                                                )));
                                            }
                                        }
                                        Ok(None) => {
                                            debug!("Skipped empty quotes line");
                                        }
                                        Err(e) => {
                                            error!("Failed to parse Quotes line: {}", e);
                                        }
                                    }

                                    // 控制发送速度
                                    tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                                }
                            }
                        } else {
                            // 处理未压缩的quotes文件
                            let file = tokio::fs::File::open(file_path).await.map_err(|e| {
                                BacktestError::Data(format!("Failed to open Quotes file: {}", e))
                            })?;

                            let reader = BufReader::new(file);
                            let mut lines = reader.lines();

                            // 跳过CSV头部
                            if let Some(_header) = lines.next_line().await.map_err(|e| {
                                BacktestError::Data(format!("Failed to read header: {}", e))
                            })? {
                                debug!("Skipped CSV header for quotes file: {:?}", file_path);
                            }

                            // 逐行处理quotes数据
                            while let Some(line) = lines.next_line().await.map_err(|e| {
                                BacktestError::Data(format!("Failed to read line: {}", e))
                            })? {
                                // 检查控制命令
                                if let Ok(command) = quotes_control_rx.lock().await.try_recv() {
                                    match command {
                                        ReaderControlCommand::Stop => {
                                            info!(
                                                "Received stop command, stopping quotes processing"
                                            );
                                            return Ok(());
                                        }
                                        ReaderControlCommand::Pause => {
                                            info!(
                                                "Received pause command, pausing quotes processing"
                                            );
                                            // 等待恢复命令
                                            loop {
                                                if let Some(ReaderControlCommand::Resume) =
                                                    quotes_control_rx.lock().await.recv().await
                                                {
                                                    info!("Received resume command, resuming quotes processing");
                                                    break;
                                                }
                                            }
                                        }
                                        _ => {}
                                    }
                                }

                                // 解析并立即发送quotes数据
                                if !line.trim().is_empty() {
                                    match Self::parse_quotes_csv_line(&line) {
                                        Ok(Some(market_data)) => {
                                            if let Err(e) = quotes_output_tx.send(market_data).await
                                            {
                                                error!("Failed to send Quotes data: {}", e);
                                                return Err(BacktestError::Data(format!(
                                                    "Failed to send data: {}",
                                                    e
                                                )));
                                            }
                                        }
                                        Ok(None) => {
                                            debug!("Skipped empty quotes line");
                                        }
                                        Err(e) => {
                                            error!("Failed to parse Quotes line: {}", e);
                                        }
                                    }

                                    // 控制发送速度
                                    tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                                }
                            }
                        }

                        info!("Completed processing quotes file: {:?}", file_path);
                    }
                }
                info!("Completed processing all quotes files");
                Ok(())
            });

            // 启动trades处理任务
            let trades_task = tokio::spawn(async move {
                if !trades_files.is_empty() {
                    info!(
                        "Starting parallel processing of {} trades files",
                        trades_files.len()
                    );
                    for file_path in &trades_files {
                        info!("Processing Trades data from: {:?}", file_path);

                        // 检查状态
                        let s = trades_status.lock().await.clone();
                        match s {
                            DataReaderStatus::Stopped => {
                                info!("Trades processing stopped");
                                return Ok(());
                            }
                            DataReaderStatus::Error(ref e) => {
                                error!("Trades processing error: {}", e);
                                return Err(BacktestError::Data(e.clone()));
                            }
                            _ => {}
                        }

                        // 处理压缩文件
                        if file_path.extension().and_then(|s| s.to_str()) == Some("gz") {
                            let file = std::fs::File::open(file_path).map_err(|e| {
                                BacktestError::Data(format!("Failed to open Trades file: {}", e))
                            })?;

                            let decoder = flate2::read::GzDecoder::new(file);
                            let reader = std::io::BufReader::new(decoder);

                            // 跳过CSV头部
                            let mut lines = reader.lines();
                            if let Some(_header) = lines.next() {
                                debug!("Skipped CSV header for trades file: {:?}", file_path);
                            }

                            // 逐行处理trades数据
                            for line in lines {
                                let line = line.map_err(|e| {
                                    BacktestError::Data(format!("Failed to read line: {}", e))
                                })?;

                                // 检查控制命令
                                if let Ok(command) = trades_control_rx.lock().await.try_recv() {
                                    match command {
                                        ReaderControlCommand::Stop => {
                                            info!(
                                                "Received stop command, stopping trades processing"
                                            );
                                            return Ok(());
                                        }
                                        ReaderControlCommand::Pause => {
                                            info!(
                                                "Received pause command, pausing trades processing"
                                            );
                                            // 等待恢复命令
                                            loop {
                                                if let Some(ReaderControlCommand::Resume) =
                                                    trades_control_rx.lock().await.recv().await
                                                {
                                                    info!("Received resume command, resuming trades processing");
                                                    break;
                                                }
                                            }
                                        }
                                        _ => {}
                                    }
                                }

                                // 解析并立即发送trades数据
                                if !line.trim().is_empty() {
                                    match Self::parse_trades_csv_line(&line) {
                                        Ok(Some(market_data)) => {
                                            if let Err(e) = trades_output_tx.send(market_data).await
                                            {
                                                error!("Failed to send Trades data: {}", e);
                                                return Err(BacktestError::Data(format!(
                                                    "Failed to send data: {}",
                                                    e
                                                )));
                                            }
                                        }
                                        Ok(None) => {
                                            debug!("Skipped empty trades line");
                                        }
                                        Err(e) => {
                                            error!("Failed to parse Trades line: {}", e);
                                        }
                                    }

                                    // 控制发送速度
                                    tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                                }
                            }
                        } else {
                            // 处理未压缩的trades文件
                            let file = tokio::fs::File::open(file_path).await.map_err(|e| {
                                BacktestError::Data(format!("Failed to open Trades file: {}", e))
                            })?;

                            let reader = BufReader::new(file);
                            let mut lines = reader.lines();

                            // 跳过CSV头部
                            if let Some(_header) = lines.next_line().await.map_err(|e| {
                                BacktestError::Data(format!("Failed to read header: {}", e))
                            })? {
                                debug!("Skipped CSV header for trades file: {:?}", file_path);
                            }

                            // 逐行处理trades数据
                            while let Some(line) = lines.next_line().await.map_err(|e| {
                                BacktestError::Data(format!("Failed to read line: {}", e))
                            })? {
                                // 检查控制命令
                                if let Ok(command) = trades_control_rx.lock().await.try_recv() {
                                    match command {
                                        ReaderControlCommand::Stop => {
                                            info!(
                                                "Received stop command, stopping trades processing"
                                            );
                                            return Ok(());
                                        }
                                        ReaderControlCommand::Pause => {
                                            info!(
                                                "Received pause command, pausing trades processing"
                                            );
                                            // 等待恢复命令
                                            loop {
                                                if let Some(ReaderControlCommand::Resume) =
                                                    trades_control_rx.lock().await.recv().await
                                                {
                                                    info!("Received resume command, resuming trades processing");
                                                    break;
                                                }
                                            }
                                        }
                                        _ => {}
                                    }
                                }

                                // 解析并立即发送trades数据
                                if !line.trim().is_empty() {
                                    match Self::parse_trades_csv_line(&line) {
                                        Ok(Some(market_data)) => {
                                            if let Err(e) = trades_output_tx.send(market_data).await
                                            {
                                                error!("Failed to send Trades data: {}", e);
                                                return Err(BacktestError::Data(format!(
                                                    "Failed to send data: {}",
                                                    e
                                                )));
                                            }
                                        }
                                        Ok(None) => {
                                            debug!("Skipped empty trades line");
                                        }
                                        Err(e) => {
                                            error!("Failed to parse Trades line: {}", e);
                                        }
                                    }

                                    // 控制发送速度
                                    tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                                }
                            }
                        }

                        info!("Completed processing trades file: {:?}", file_path);
                    }
                }
                info!("Completed processing all trades files");
                Ok(())
            });

            // 等待两个任务都完成
            let (quotes_result, trades_result) = tokio::join!(quotes_task, trades_task);

            // 检查任务结果
            match quotes_result {
                Ok(Ok(())) => info!("Quotes processing completed successfully"),
                Ok(Err(e)) => {
                    error!("Quotes processing failed: {}", e);
                    return Err(e);
                }
                Err(e) => {
                    error!("Quotes task panicked: {}", e);
                    return Err(BacktestError::Data(format!("Quotes task panicked: {}", e)));
                }
            }

            match trades_result {
                Ok(Ok(())) => info!("Trades processing completed successfully"),
                Ok(Err(e)) => {
                    error!("Trades processing failed: {}", e);
                    return Err(e);
                }
                Err(e) => {
                    error!("Trades task panicked: {}", e);
                    return Err(BacktestError::Data(format!("Trades task panicked: {}", e)));
                }
            }

            info!("All combined Tardis data files processed successfully with parallel processing");
            Ok(())
        }));

        Ok(())
    }
}
